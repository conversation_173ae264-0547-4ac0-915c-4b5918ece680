{"common": {"cancel": "<PERSON><PERSON>", "live": "Langsung"}, "glossary": {"busArrivals": "<PERSON><PERSON><PERSON><PERSON> bas", "passingRoutes": "<PERSON><PERSON> yang <PERSON>", "popOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seatsAvailable": "Tempat duduk disediakan", "standingAvailable": "<PERSON><PERSON><PERSON> tersedia", "limitedStanding": "Kedudukan terhad", "arriving": "Arr", "multiRouteMode": "<PERSON>d berbilang la<PERSON>an", "weekdaysShort": "MG", "weekdays": "<PERSON>", "saturdaysShort": "SAB", "saturdays": "Sabtu", "sundaysPublicHolidaysShort": "AHD", "sundaysPublicHolidays": "<PERSON><PERSON> <PERSON> <PERSON>i <PERSON>", "service": "Perkhidmatan", "firstBus": "<PERSON><PERSON>", "lastBus": "<PERSON><PERSON>", "arrivingMinutes_0": "{{count}}m", "nRoutes_0": "{{count}} la<PERSON>an", "nStops_0": "{{count}} hentian", "nServices_0": "{{count}} perkhidmatan"}, "app": {"name": "BusRouter SG", "title": "$t(app.name) - <PERSON><PERSON><PERSON>jah Laluan Bas Singapura", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> Sing<PERSON>", "description": "<PERSON><PERSON><PERSON> per<PERSON> dan laluan bas pada peta untuk semua perkhidmatan bas di Singapura, dengan masa ketibaan bas masa nyata dan gambaran kese<PERSON><PERSON>han laluan setiap hentian bas."}, "about": {"disclaimerCopyright": "<0><PERSON><PERSON></0> & <2>divis<PERSON><PERSON><PERSON><PERSON></2> oleh <4>@cheeaun</4> . Data <6>© LTA</6> .", "helpTranslations": "<PERSON><PERSON><PERSON> dengan terjemahan", "sisterSites": "Tapak kakak: <1>🚆 RailRouter SG</1> <3>🚖 TaxiRouter SG</3>", "liking": "❤️ Suka $t(app.name)?", "treatCoffee": "☕️ Sokong kerja saya & layan saya kopi!", "explore": "<PERSON><PERSON> explore"}, "search": {"placeholder": "<PERSON>i perkhidmatan bas atau perhentian"}, "service": {"title": "Perkhidmatan bas {{serviceNumber}}: {{serviceName}} - $t(app.name)", "titleMultiple": "Perkhidmatan bas: {{serviceNumbersNames}} - $t(app.name)", "oppositeLegend": "<PERSON><PERSON><PERSON> bas dengan arah perkhidmatan yang bertentangan"}, "stop": {"title": "<PERSON><PERSON><PERSON> bas {{stopNumber}}: {{stopName}} - $t(app.name)", "titleRoutes": "<PERSON><PERSON> yang melalui perhentian bas {{stopNumber}}: {{stopName}} - $t(app.name)", "firstLastBus": "Bas pertama/terakhir", "multipleDirectionsWarning": "Sesetengah perkhidmatan menyediakan pelbagai arah. <PERSON><PERSON> \"<PERSON>tibaan bas\" untuk melihat butiran lanjut.", "liveBusTrack_0": "{{count}} bas kini berada di landasan."}, "passingRoutes": {"passingRoutes_0": "{{count}} la<PERSON>an yang lalu"}, "multiRoute": {"addRoute": "<PERSON><PERSON> laluan bas lain", "showingServices_0": "Menunjukkan {{count}} perkhidmatan", "intersectingStops_0": "{{count}} per<PERSON><PERSON> bersilang"}, "arrivals": {"invalidBusStopCode": "<PERSON>d per<PERSON> bas tidak sah.", "preHeading": "<PERSON><PERSON> k<PERSON> bas", "title": "<PERSON><PERSON> k<PERSON> bas", "titleStop": "<PERSON><PERSON> bas untuk {{stopNumber}} - {{stopName}}", "multipleVisitsLegend": "Lawatan pertama atau kedua. Bas berhenti di sini dua kali, pergi ke arah yang berbeza. Semak destinasi sebelum menaiki pesawat.", "wheelchairDisclaimer": "<0/> <PERSON><PERSON><PERSON> bas awam dalam perkhidmatan hasil adalah <2>boleh diakses kerusi roda</2> . <PERSON> tidak boleh diakses akan ditandakan dengan ikon ini <4/>"}, "firstLast": {"title": "Anggaran masa ketibaan bas pertama & terak<PERSON> untuk {{stopNumber}}: {{stopName}}", "busStopCodeNotFound": "<PERSON><PERSON> bas tidak di<PERSON>.", "preHeading": "Anggaran masa ketibaan bas pertama & terakhir"}}