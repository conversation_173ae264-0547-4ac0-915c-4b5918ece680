export default function useRafInterval(fn, delay, conditions = []) {
  const savedFn = useRef();
  useEffect(() => {
    savedFn.current = fn;
  });
  useEffect(() => {
    let timeoutID, rafID, cleanup;
    function tick() {
      cleanup = savedFn.current();
      timeoutID = setTimeout(() => {
        rafID = requestAnimationFrame(tick);
      }, delay);
    }
    tick();
    return () => {
      cancelAnimationFrame(rafID);
      clearTimeout(timeoutID);
      cleanup();
    };
  }, [delay, ...conditions]);
};