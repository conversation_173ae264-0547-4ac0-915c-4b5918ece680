body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0;
  margin: 0;
  background-color: #f7f7f7;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

main {
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  padding: 0;
  margin: 1em 10px;
  font-size: 1em;
}
h1 b {
  font-size: 1.2em;
}

.stop-tag {
  display: inline-block;
  padding: 2px 5px;
  border-radius: 4px;
  background-color: #f01b4822;
  color: #f01b48;
  flex-shrink: 0;
  font-variant-numeric: tabular-nums;
  font-weight: 500;
  margin: 2px 0;
  font-size: 0.8em;
}

.legend {
  margin: 10px;
  padding: 0;
  font-size: 0.8em;
  color: #666;
}
.legend span {
  display: inline-block;
}
.legend > span {
  margin-right: 1.5em;
  margin-bottom: 0.5em;
}
.legend .abbr {
  padding: 4px 7px;
  font-size: 0.9em;
  color: #999;
  border-radius: 3px;
  border: 1px solid #aaa;
  background-color: #fff;
}

table {
  width: 100%;
  border: 0;
  border-spacing: 0;
  clear: both;
}

table td,
table th {
  padding: 5px 8px;
}

table td[title='-'] {
  color: rgba(0, 0, 0, 0.3);
  pointer-events: none;
  user-select: none;
}

table thead tr > * {
  white-space: nowrap;
  position: sticky;
  top: 0;
  background-color: rgba(247, 247, 247, 0.9);
  border-bottom: 1px solid #ccc;
  z-index: 100;
}
table thead th {
  font-weight: normal;
  color: #999;
  text-transform: uppercase;
  font-size: 0.8em;
}
table thead th:first-child {
  text-align: left;
}

table td[rowspan] {
  font-size: 1.8em;
}

table tbody {
  background-color: #fff;
}
table tbody > tr:first-child > td[rowspan],
table tbody > tr:last-child > * {
  border-bottom: 1px solid #ccc;
}
table tbody th abbr {
  font-weight: normal;
  color: #999;
  text-align: right;
  font-size: 0.8em;
}
table tbody th ~ td {
  text-align: center;
  font-feature-settings: 'tnum';
  font-variant-numeric: tabular-nums;
}
table tbody tr:nth-child(2) > * {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.insignificant {
  opacity: 0.5;
}

abbr {
  text-decoration: none;
}

.timerange-header,
.time-cell,
.timerange-note {
  display: none;
}
.timerange-header > span {
  display: inline-block;
  width: 25%;
  text-align: left;
}
.timerange-indicator {
  top: 0;
  position: absolute;
  /* background-color: #007aff; */
  background-image: linear-gradient(
    rgba(255, 255, 255, 0) 2.5em,
    #007aff 2.5em,
    #007aff 60%,
    rgba(255, 255, 255, 0) 90%
  );
  width: 2px;
  height: 100vh;
  pointer-events: none;
  font-feature-settings: 'tnum';
  font-variant-numeric: tabular-nums;
}
.timerange-indicator > span {
  position: absolute;
  top: 2em;
  transform: translateX(-50%);
  padding: 3px 6px;
  border-radius: 123123px;
  background-color: #007aff;
  color: #fff;
  line-height: 1;
  font-size: smaller;
}
.time-cell {
  background: linear-gradient(
    to right,
    transparent,
    transparent 24.8%,
    #eee 24.9%,
    #eee 25.1%,
    transparent 25.2%,
    transparent 49.8%,
    #ddd 49.9%,
    #ddd 50.1%,
    transparent 50.2%,
    transparent 74.8%,
    #eee 74.9%,
    #eee 75.1%,
    transparent 75.2%,
    transparent
  );
}
.time-ranger {
  position: relative;
  width: 100%;
  min-width: 240px;
  background-color: #ddd;
  height: 5px;
  border-radius: 2020px;
  overflow: hidden;
}
.time-ranger.nada {
  background-color: #eee;
}
.time-ranger .bar {
  position: absolute;
  border-radius: 2020px;
  height: 5px;
  opacity: 0.5;
  min-width: 5px;
  background-color: #007aff;
}
.time-duration {
  line-height: 1;
  display: block;
  font-size: 10px;
  color: #007aff;
  width: 100%;
  text-align: right;
  z-index: 1;
  position: relative;
  margin-bottom: -10px;
}

@media (min-width: 640px) {
  .timerange-header,
  .time-cell {
    display: table-cell;
  }
  .timerange-note {
    display: block;
  }

  .ads {
    float: right;
    width: 320px;
    margin-top: -1em;
  }
}

tfoot td {
  font-size: 0.9em;
  padding: 10px 8px;
  color: #999;
}
tfoot p {
  margin-top: 0;
  padding-top: 0;
}
tfoot a {
  color: inherit;
}

.placeholder {
  color: #aaa;
  letter-spacing: -1px;
  word-spacing: 1ex;
  animation: glowing infinite alternate 1s ease-in-out both;
  pointer-events: none;
  user-select: none;
}
@keyframes glowing {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 0.8;
  }
}

blink {
  animation: blink 0.5s linear infinite alternate;
}
@keyframes blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
