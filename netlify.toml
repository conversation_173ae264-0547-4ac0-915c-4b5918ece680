[Settings]

ID = "busrouter-sg"

[context.production]
  publish = "dist/"
  command = "npm run build"

[context.v1]
  publish = ""
  command = ""

[[redirects]]
  from = "https://next.busrouter.sg/"
  to = "https://busrouter.sg/"
  status = 301
  force = true

[[redirects]]
  from = "/staticmaps/*"
  to = "https://api.mapbox.com/styles/v1/cheeaun/ckn18umqw1jsi17nymmpdinba/static/:splat"
  status = 200

[[redirects]]
  from = "/mapbox-tiles/*"
  to = "https://a.tiles.mapbox.com/:splat"
  status = 200

[[redirects]]
  from = "/ads.txt"
  to = "https://cdn4.buysellads.net/ads.txt"
  force = true

[[plugins]]
package = "netlify-plugin-ttl-cache"
  [plugins.inputs]
  path = "dist"