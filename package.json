{"name": "busrouter-sg", "version": "2.1.0", "description": "Singapore Bus Routes Explorer", "author": "<PERSON>", "license": "MIT", "scripts": {"start": "parcel serve index.html bus-arrival/index.html bus-first-last/index.html visualization/index.html -p 8888", "build": "parcel build index.html bus-arrival/index.html bus-first-last/index.html visualization/index.html --no-cache", "postbuild": "cpy {favicon.ico,ads.txt} dist && cpy well-known/* dist/.well-known && node tasks/generateHeaders", "prod": "npx serve dist/"}, "dependencies": {"@deck.gl/core": "9.1.12", "@deck.gl/layers": "9.1.12", "@deck.gl/mapbox": "9.1.12", "@mapbox/mapbox-gl-language": "~1.0.1", "@sentry/react": "~9.33.0", "@sentry/tracing": "~7.120.3", "cheap-ruler": "~4.0.0", "date-fns": "~4.1.0", "fuse.js": "~7.1.0", "i18next": "~25.2.1", "i18next-browser-languagedetector": "~8.2.0", "just-intersect": "~4.3.0", "lscache": "~1.3.2", "mapbox-gl": "~3.13.0", "maplibre-gl": "~5.6.0", "preact": "~10.26.9", "react-i18next": "^13.5.0", "use-resize-observer": "~9.1.0", "workbox-cacheable-response": "~7.3.0", "workbox-expiration": "~7.3.0", "workbox-google-analytics": "~7.3.0", "workbox-routing": "~7.3.0", "workbox-strategies": "~7.3.0"}, "devDependencies": {"@babel/core": "~7.27.7", "@babel/plugin-transform-react-jsx": "~7.27.1", "@mapbox/polyline": "~1.2.1", "@parcel/babel-plugin-transform-runtime": "~2.15.4", "@parcel/babel-preset-env": "~2.15.4", "@parcel/config-default": "~2.15.4", "@parcel/packager-raw-url": "~2.15.4", "@parcel/transformer-image": "~2.15.4", "@parcel/transformer-webmanifest": "~2.15.4", "@turf/circle": "~7.2.0", "@turf/helpers": "~7.2.0", "@turf/simplify": "~7.2.0", "babel-plugin-transform-remove-console": "~6.9.4", "cpy-cli": "5.0.0", "netlify-plugin-ttl-cache": "~1.0.2", "parcel": "~2.15.4", "process": "^0.11.10"}, "alias": {"react": "preact/compat"}, "browserslist": [">0.25%", "not dead", "not ie 11", "not chrome < 51", "not op_mini all", "not Android > 1", "not safari < 10"]}